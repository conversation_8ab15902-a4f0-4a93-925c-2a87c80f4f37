<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Figma按钮对比 - 优化前后</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="glass-button.css">
    <style>
        body {
            background: #F1F1F1;
            padding: 40px;
        }

        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            max-width: 1000px;
            margin: 0 auto;
            align-items: center;
        }

        .comparison-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .comparison-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }

        .comparison-subtitle {
            font-size: 16px;
            color: #666;
            text-align: center;
            margin-bottom: 10px;
        }

        .figma-link {
            color: #0066CC;
            text-decoration: none;
            font-size: 14px;
        }

        .figma-link:hover {
            text-decoration: underline;
        }

        /* 为glass-button组件重置一些样式 */
        .glass-button-container {
            background: none;
            border: none;
            padding: 0;
            cursor: pointer;
            position: relative;
            width: 170px;
            height: 170px;
            transition: transform 0.3s ease;
        }

        .glass-button-container:hover {
            transform: translateY(-2px);
        }

        .glass-button-container:active {
            transform: translateY(0px);
        }
    </style>
</head>
<body>
    <div class="comparison-container">
        <div class="comparison-item">
            <h2 class="comparison-title">优化后版本</h2>
            <p class="comparison-subtitle">基于你提供的Figma CSS数据精确还原</p>

            <button class="button-container">
                <!-- 模糊椭圆层 -->
                <div class="blur-ellipse ellipse-4"></div>
                <div class="blur-ellipse ellipse-5"></div>
                <div class="blur-ellipse ellipse-3"></div>
                <div class="blur-ellipse ellipse-2"></div>

                <div class="button-body">
                    <div class="icon">
                        <svg width="72" height="72" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="iconGradient1" x1="0.177" y1="0.177" x2="0.82" y2="0.82">
                                    <stop offset="17.69%" stop-color="#CEC6D5" />
                                    <stop offset="82.01%" stop-color="#E8E1EE" />
                                </linearGradient>
                            </defs>
                            <path d="M12 5L12 19M12 5L6 11M12 5L18 11" stroke="url(#iconGradient1)" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
            </button>

            <div style="text-align: center; font-size: 14px; color: #666;">
                <p>✅ 精确的渐变角度 (223.21deg)</p>
                <p>✅ 正确的透明度 (0.2)</p>
                <p>✅ 精确的模糊值 (15px/20px)</p>
                <p>✅ 正确的椭圆位置</p>
            </div>
        </div>

        <div class="comparison-item">
            <h2 class="comparison-title">Glass Button版本</h2>
            <p class="comparison-subtitle">通用玻璃拟态按钮组件</p>

            <button class="glass-button-container glass-button">
                <div class="button-background">
                    <div class="blur-circle blur-circle-1"></div>
                    <div class="blur-circle blur-circle-2"></div>
                    <div class="blur-circle blur-circle-3"></div>
                    <div class="blur-circle blur-circle-4"></div>
                    <div class="blur-circle blur-circle-5"></div>
                </div>
                <div class="button-icon">
                    <svg width="69" height="69" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="iconGradient2" x1="0.177" y1="0.177" x2="0.82" y2="0.82">
                                <stop offset="17.69%" stop-color="#CEC6D5" />
                                <stop offset="82.01%" stop-color="#E8E1EE" />
                            </linearGradient>
                        </defs>
                        <path d="M12 5L12 19M12 5L6 11M12 5L18 11" stroke="url(#iconGradient2)" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </button>

            <div style="text-align: center; font-size: 14px; color: #666;">
                <p>🔧 可自定义的组件</p>
                <p>🔧 支持多种尺寸</p>
                <p>🔧 易于集成</p>
                <p>🔧 现代CSS技术</p>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 40px;">
        <a href="https://www.figma.com/design/avfSTaLt8T4Gkukr75MoVR/玻璃拟态按钮" class="figma-link" target="_blank">
            查看原始Figma设计
        </a>
    </div>
</body>
</html>
