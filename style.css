/* 基础样式和背景 */
body {
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #FFFFFF;
}

/* 按钮总容器 */
.button-container {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    position: relative;
    width: 170px;
    height: 170px;
    transition: transform 0.1s ease-out;
    font-size: 100px;
}

/* 模糊椭圆基础样式 */
.blur-ellipse {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
}

/* Ellipse 4 - 左上模糊圆 */
.ellipse-4 {
    width: 110px;
    height: 110px;
    left: -5px;
    top: -14px;
    background: #FCF5FD;
    border: 1px solid rgba(0, 0, 0, 0.1);
    filter: blur(15px);
}

/* Ellipse 5 - 右上模糊圆 */
.ellipse-5 {
    width: 110px;
    height: 110px;
    left: 67px;
    top: -14px;
    background: #EDF8FC;
    filter: blur(15px);
}

/* Ellipse 3 - 中心大模糊圆 */
.ellipse-3 {
    width: 170px;
    height: 170px;
    left: 14px;
    top: 15px;
    background: #C1BFBF;
    filter: blur(20px);
}

/* Ellipse 2 - 底部白色模糊圆 */
.ellipse-2 {
    width: 120px;
    height: 120px;
    left: 50px;
    top: 55px;
    background: #FFFFFF;
    filter: blur(15px);
}

/* 按钮主体 */
.button-body {
    position: relative;
    width: 1.7em;
    height: 1.7em;
    border-radius: 50%;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    transition: box-shadow 0.2s ease-out;

    /* 基于Figma数据的精确渐变 */
    background: linear-gradient(223.21deg, rgba(217, 228, 242, 0.2) 12.88%, rgba(254, 249, 253, 0.2) 84.66%);

    /* 磨砂效果 */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    /* 光影效果 */
    box-shadow:
        inset 1px 1px 1px #FFFFFF;
}

/* 添加主要背景层 */
.button-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(136.39deg, #EBF2FA 15.81%, #F8F3FF 70.55%, #FFFFFF 88.8%);
    box-shadow: inset 1px 1px 1px #FFFFFF;
    z-index: -1;
}

/* 图标样式 */
.icon {
    position: relative;
    z-index: 3;
    width: 0.9em;
    height: 0.9em;
    display: flex;
    justify-content: center;
    align-items: center;
    /* 基于Figma数据的精确阴影效果 */
    filter: drop-shadow(0px 1px 1px rgba(0, 0, 0, 0.1));
    transition: transform 0.2s ease-out;
}

.icon svg {
    width: 100%;
    height: 100%;
    /* 添加内阴影效果 */
    filter: drop-shadow(0px 1px 1px rgba(0, 0, 0, 0.1));
}

.icon svg path {
    /* 确保图标使用正确的渐变 */
    filter: drop-shadow(inset 0px 1px 1px #FDFDFD);
}

/* 点击效果 */
.button-container:active {
    transform: scale(0.96);
}

.button-container:active .button-body {
    box-shadow: inset 0.02em 0.02em 0.1em rgba(68, 71, 75, 0.2);
}

.button-container:active .icon {
    transform: translateY(0.02em);
}