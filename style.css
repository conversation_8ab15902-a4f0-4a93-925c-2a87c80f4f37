/* 基础样式和背景 */
body {
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #E6E7EE;
}

.background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(#b8b8b8 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.5;
    z-index: -1;
}

/* 按钮总容器 */
.button-container {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    position: relative;
    width: 170px;
    height: 170px;
    transition: transform 0.1s ease-out;
    font-size: 100px;
}

/* 按钮主体 */
.button-body {
    position: relative;
    width: 1.7em; 
    height: 1.7em;
    border-radius: 50%;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden; 
    transition: box-shadow 0.2s ease-out;

    /* ↓↓↓ 关键改动：将透明度调整为 80% (alpha = 0.2) ↓↓↓ */
    background: linear-gradient(
        135deg, 
        rgba(235, 242, 250, 0) 0%, /* alpha 从 0.5 改为 0.2 */
        rgba(248, 243, 255, 0) 75%, /* alpha 从 0.5 改为 0.2 */
        rgba(255, 255, 255, 0) 100%  /* alpha 从 0.5 改为 0.2 */
    );
    /* 磨砂效果 */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    /* 光影效果 */
    box-shadow: 
        inset 0 0 0 0.02em rgba(255, 255, 255, 0.4),
        0.05em 0.15em 0.2em rgba(68, 71, 75, 0.08),
        inset 0.02em 0.02em 0.01em rgba(255, 255, 255, 1),
        -0.15em -0.15em 0.15em -0.08em #edf8fc,
        0.15em -0.15em 0.15em -0.08em #fcf5fd;
}

/* 图标样式 */
.icon {
    position: relative;
    z-index: 3;
    width: 0.72em;
    height: 0.72em;
    display: flex;
    justify-content: center;
    align-items: center;
    filter: drop-shadow(0px 0.01em 0.01em #FFFFFF);
    transition: transform 0.2s ease-out;
}
.icon svg {
  width: 100%;
  height: 100%;
}

/* 点击效果 */
.button-container:active {
    transform: scale(0.96);
}

.button-container:active .button-body {
    box-shadow: inset 0.02em 0.02em 0.1em rgba(68, 71, 75, 0.2);
}

.button-container:active .icon {
    transform: translateY(0.02em);
}