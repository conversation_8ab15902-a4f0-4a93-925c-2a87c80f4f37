# 玻璃拟态按钮组件

这是一个基于Figma设计的玻璃拟态（Glassmorphism）按钮组件，使用HTML和CSS实现。

## 文件说明

- `index.html` - 基于Figma CSS数据精确还原的版本
- `style.css` - 精确还原版本的样式文件
- `glass-button.html` - 通用玻璃拟态按钮组件示例
- `glass-button.css` - 通用组件的样式文件
- `demo.html` - 演示页面，展示不同尺寸的按钮
- `comparison.html` - 对比页面，展示优化前后的效果
- `button-icon.svg` - 从Figma导出的原始SVG图标
- `README.md` - 说明文档

## 版本说明

### 精确还原版本 (index.html)
基于你提供的Figma CSS数据进行精确还原：
- ✅ 精确的渐变角度 (223.21deg)
- ✅ 正确的透明度值 (0.2)
- ✅ 精确的模糊值 (15px/20px)
- ✅ 正确的椭圆位置和颜色
- ✅ 164.08deg的图标渐变角度

### 通用组件版本 (glass-button.html)
可复用的玻璃拟态按钮组件：
- 🔧 支持多种尺寸变体
- 🔧 易于自定义颜色
- 🔧 现代CSS技术实现
- 🔧 良好的浏览器兼容性

## 设计特点

### 玻璃拟态效果
- 半透明渐变背景
- 多层模糊圆圈营造深度感
- 内阴影和外阴影结合
- 渐变边框效果

### 交互效果
- 悬停时按钮轻微上浮
- 点击时按钮下沉
- 图标缩放动画
- 平滑的过渡动画

### 技术实现
- 使用CSS渐变创建玻璃质感
- 利用伪元素实现复杂的边框效果
- 多个模糊圆圈叠加营造景深
- SVG图标支持完美缩放

## 使用方法

### 基本用法

```html
<button class="glass-button">
    <div class="button-background">
        <div class="blur-circle blur-circle-1"></div>
        <div class="blur-circle blur-circle-2"></div>
        <div class="blur-circle blur-circle-3"></div>
        <div class="blur-circle blur-circle-4"></div>
        <div class="blur-circle blur-circle-5"></div>
    </div>
    <div class="button-icon">
        <!-- 在这里放置你的SVG图标 -->
    </div>
</button>
```

### 尺寸变体

```html
<!-- 小尺寸按钮 -->
<button class="glass-button glass-button-small">
    <!-- 内容同上 -->
</button>

<!-- 大尺寸按钮 -->
<button class="glass-button glass-button-large">
    <!-- 内容同上 -->
</button>
```

### 颜色变体

```html
<!-- 次要按钮样式 -->
<button class="glass-button glass-button-secondary">
    <!-- 内容同上 -->
</button>
```

## 自定义

### 修改颜色
在CSS中修改以下变量来改变按钮颜色：

```css
.glass-button {
    background: linear-gradient(135deg, #你的颜色1 0%, #你的颜色2 100%);
}

.glass-button::after {
    background: linear-gradient(135deg, #你的颜色1 0%, #你的颜色2 100%);
}
```

### 修改尺寸
调整按钮的宽度和高度：

```css
.glass-button {
    width: 你的宽度px;
    height: 你的高度px;
}
```

### 替换图标
将SVG图标替换为你自己的图标，确保保持相同的结构和滤镜效果。

## 浏览器兼容性

- Chrome 88+
- Firefox 87+
- Safari 14+
- Edge 88+

## 原始设计

本组件基于Figma设计文件创建：
https://www.figma.com/design/avfSTaLt8T4Gkukr75MoVR/玻璃拟态按钮

## 许可证

MIT License
