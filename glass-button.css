/* 玻璃拟态按钮样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: #F1F1F1;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.glass-button {
    position: relative;
    width: 170px;
    height: 170px;
    border: none;
    border-radius: 100px;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;

    /* 主要渐变背景 - 基于Figma精确数据 */
    background: linear-gradient(223.21deg, rgba(217, 228, 242, 0.2) 12.88%, rgba(254, 249, 253, 0.2) 84.66%);

    /* 边框效果 - 使用伪元素实现渐变边框 */
    position: relative;
}

.glass-button::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, #D9EAF2 0%, #FEFBFD 100%);
    border-radius: 100px;
    z-index: -1;
}

.glass-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(136.39deg, #EBF2FA 15.81%, #F8F3FF 70.55%, #FFFFFF 88.8%);
    border-radius: 100px;
    z-index: 0;

    /* 主要阴影效果 */
    box-shadow:
        inset 1px 1px 1px 0px rgba(255, 255, 255, 1);
}

.glass-button:hover {
    transform: translateY(-2px);
}

.glass-button:hover::after {
    box-shadow:
        inset 1px 1px 1px 0px rgba(255, 255, 255, 1);
}

.glass-button:active {
    transform: translateY(0px);
}

.glass-button:active::after {
    box-shadow:
        inset 1px 1px 1px 0px rgba(255, 255, 255, 1);
}

.button-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 100px;
    overflow: hidden;
    z-index: 1;
}

/* 模糊圆圈效果 */
.blur-circle {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
}

.blur-circle-1 {
    width: 110px;
    height: 110px;
    background: #FCF5FD;
    border: 1px solid rgba(0, 0, 0, 0.1);
    filter: blur(15px);
    top: -14px;
    left: -5px;
}

.blur-circle-2 {
    width: 110px;
    height: 110px;
    background: #EDF8FC;
    filter: blur(15px);
    top: -14px;
    right: -5px;
}

.blur-circle-3 {
    width: 170px;
    height: 170px;
    background: #C1BFBF;
    filter: blur(20px);
    top: 15px;
    left: 21px;
}

.blur-circle-4 {
    width: 120px;
    height: 120px;
    background: #FFFFFF;
    filter: blur(15px);
    top: 55px;
    left: 50px;
}

.blur-circle-5 {
    width: 170px;
    height: 170px;
    background: linear-gradient(135deg, #EBF2FA 0%, #F8F3FF 75%, #FFFFFF 100%);
    box-shadow: inset 1px 1px 1px 0px rgba(255, 255, 255, 1);
    filter: none;
    top: 0;
    left: 0;
    z-index: 1;
}

/* 按钮图标 */
.button-icon {
    position: relative;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.button-icon svg {
    width: 67px;
    height: 67px;
    transition: all 0.3s ease;
}

/* 按钮交互效果 */
.glass-button:hover .button-icon svg {
    transform: scale(1.05);
}

.glass-button:active .button-icon svg {
    transform: scale(0.95);
}
