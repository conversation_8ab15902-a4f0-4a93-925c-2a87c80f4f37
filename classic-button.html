<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经典拟物化按钮</title>
    <style>
        body {
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .classic-button {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: #ffffff;
            border: none;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;

            /* 非常微妙的外阴影 */
            box-shadow:
                0 8px 16px rgba(0, 0, 0, 0.1),
                0 2px 4px rgba(0, 0, 0, 0.06);
        }

        .classic-button:hover {
            transform: translateY(-1px);
            box-shadow:
                0 10px 20px rgba(0, 0, 0, 0.12),
                0 4px 8px rgba(0, 0, 0, 0.08);
        }

        .classic-button:active {
            transform: translateY(0px);
            box-shadow:
                0 4px 8px rgba(0, 0, 0, 0.08),
                0 1px 2px rgba(0, 0, 0, 0.04);
        }

        .arrow-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
        }

        .arrow-icon svg {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <button class="classic-button">
        <div class="arrow-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 19V5M5 12L12 5L19 12"
                      stroke="#b8a8c8"
                      stroke-width="2.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"/>
            </svg>
        </div>
    </button>
</body>
</html>
